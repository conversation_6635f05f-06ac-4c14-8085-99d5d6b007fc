#!/usr/bin/env node

/**
 * Production Login Issues Diagnostic & Fix Script
 * 
 * Identifies and fixes critical login issues in production environment
 */

console.log('🔍 Diagnosing Production Login Issues...\n')

// Load environment variables
require('dotenv').config({ path: '.env.local' })

console.log('📋 1. Environment Configuration Analysis')
console.log('==========================================')

const currentDomain = process.env.DOMAIN
const studentDomain = process.env.STUDENT_DOMAIN
const adminDomain = process.env.ADMIN_DOMAIN
const nodeEnv = process.env.NODE_ENV

console.log(`Current DOMAIN: ${currentDomain}`)
console.log(`Student Domain: ${studentDomain}`)
console.log(`Admin Domain: ${adminDomain}`)
console.log(`Node Environment: ${nodeEnv}`)

console.log('\n🌐 2. Production URL Analysis')
console.log('==============================')

const productionUrls = [
  'https://website-shalatyuk-app.hv3ahr.easypanel.host/admin/home',
  'https://website-shalatyuk-app.hv3ahr.easypanel.host/student'
]

console.log('Production URLs detected:')
productionUrls.forEach(url => {
  const hostname = new URL(url).hostname
  console.log(`  - ${hostname}`)
})

const productionHostname = 'website-shalatyuk-app.hv3ahr.easypanel.host'
const configuredDomains = [currentDomain, studentDomain, adminDomain]

console.log('\n🚨 3. Domain Mismatch Detection')
console.log('================================')

const domainMismatch = !configuredDomains.some(domain => 
  productionHostname.includes(domain) || domain.includes(productionHostname)
)

if (domainMismatch) {
  console.log('❌ CRITICAL ISSUE: Domain mismatch detected!')
  console.log(`   Production hostname: ${productionHostname}`)
  console.log(`   Configured domains: ${configuredDomains.join(', ')}`)
  console.log('   This will cause cookie and session issues!')
} else {
  console.log('✅ Domain configuration matches production URLs')
}

console.log('\n🍪 4. Cookie Domain Analysis')
console.log('=============================')

// Simulate cookie domain logic
function analyzeCookieDomain(hostname) {
  const isProduction = process.env.NODE_ENV === 'production'
  const isDevelopment = process.env.NODE_ENV === 'development'
  
  console.log(`Analyzing for hostname: ${hostname}`)
  console.log(`Environment: ${process.env.NODE_ENV || 'development'}`)
  
  // Check if localhost
  if (hostname.includes('localhost')) {
    console.log('  Cookie domain: undefined (same-origin only)')
    return undefined
  }
  
  // Extract base domain
  const parts = hostname.split('.')
  if (parts.length >= 2) {
    const baseDomain = parts.slice(-2).join('.')
    const cookieDomain = `.${baseDomain}`
    console.log(`  Base domain: ${baseDomain}`)
    console.log(`  Cookie domain: ${cookieDomain}`)
    return cookieDomain
  }
  
  console.log('  Cookie domain: undefined (invalid hostname)')
  return undefined
}

const productionCookieDomain = analyzeCookieDomain(productionHostname)
const configuredCookieDomain = analyzeCookieDomain(currentDomain)

console.log('\n🔧 5. Session Management Analysis')
console.log('===================================')

console.log('Checking session management configuration...')

// Check if session management files exist
const fs = require('fs')
const path = require('path')

const criticalFiles = [
  'lib/utils/cookie-security.ts',
  'lib/domain/usecases/enhanced-auth.ts',
  'app/api/auth/admin/login/route.ts',
  'app/api/auth/student/login/route.ts',
  'app/api/auth/check-session/route.ts'
]

criticalFiles.forEach(file => {
  const exists = fs.existsSync(path.join(__dirname, '..', file))
  console.log(`${exists ? '✅' : '❌'} ${file}`)
})

console.log('\n🎯 6. Root Cause Analysis')
console.log('==========================')

const issues = []
const solutions = []

if (domainMismatch) {
  issues.push('Domain configuration mismatch with production URL')
  solutions.push('Update environment variables to match production domain')
}

if (productionCookieDomain !== configuredCookieDomain) {
  issues.push('Cookie domain mismatch between production and configuration')
  solutions.push('Ensure cookie domain settings match production hostname')
}

if (nodeEnv !== 'production') {
  issues.push('NODE_ENV not set to production')
  solutions.push('Set NODE_ENV=production for production deployment')
}

console.log('Issues identified:')
issues.forEach((issue, index) => {
  console.log(`  ${index + 1}. ${issue}`)
})

console.log('\nSolutions required:')
solutions.forEach((solution, index) => {
  console.log(`  ${index + 1}. ${solution}`)
})

console.log('\n🔨 7. Recommended Environment Configuration')
console.log('============================================')

console.log('For production deployment at website-shalatyuk-app.hv3ahr.easypanel.host:')
console.log('')
console.log('# Production Environment Variables')
console.log('NODE_ENV=production')
console.log('DOMAIN=hv3ahr.easypanel.host')
console.log('STUDENT_DOMAIN=website-shalatyuk-app.hv3ahr.easypanel.host')
console.log('ADMIN_DOMAIN=website-shalatyuk-app.hv3ahr.easypanel.host')
console.log('')
console.log('# OR for subdomain-based routing:')
console.log('DOMAIN=hv3ahr.easypanel.host')
console.log('STUDENT_DOMAIN=student.hv3ahr.easypanel.host')
console.log('ADMIN_DOMAIN=admin.hv3ahr.easypanel.host')

console.log('\n🚀 8. Immediate Fix Steps')
console.log('==========================')

console.log('1. Update production environment variables:')
console.log('   - Set NODE_ENV=production')
console.log('   - Update DOMAIN to match production hostname')
console.log('   - Update STUDENT_DOMAIN and ADMIN_DOMAIN accordingly')
console.log('')
console.log('2. Restart the application after environment changes')
console.log('')
console.log('3. Clear browser cookies and try logging in again')
console.log('')
console.log('4. Check browser developer tools for cookie and network errors')

console.log('\n🧪 9. Testing Commands')
console.log('=======================')

console.log('Test login endpoints:')
console.log(`curl -X POST ${productionUrls[0].replace('/admin/home', '/api/auth/admin/login')} \\`)
console.log('  -H "Content-Type: application/json" \\')
console.log('  -d \'{"username":"your_username","password":"your_password"}\'')
console.log('')
console.log(`curl -X POST ${productionUrls[1].replace('/student', '/api/auth/student/login')} \\`)
console.log('  -H "Content-Type: application/json" \\')
console.log('  -d \'{"username":"your_username","password":"your_password"}\'')

console.log('\n✅ Diagnostic Complete!')
console.log('========================')

if (issues.length === 0) {
  console.log('🎉 No critical issues detected in configuration')
  console.log('   Login issues may be related to:')
  console.log('   - Network connectivity')
  console.log('   - Database connection')
  console.log('   - Redis connection')
  console.log('   - Invalid credentials')
} else {
  console.log(`⚠️  ${issues.length} critical issue(s) found that need immediate attention`)
  console.log('   Fix these issues and redeploy to resolve login problems')
}
