/**
 * Cookie Security Utilities - Production Ready
 *
 * Provides secure cookie configuration for subdomain sharing
 * with proper domain settings and security flags.
 */

import { serverConfig } from '@/lib/config'

/**
 * Get the appropriate cookie domain for subdomain sharing
 * @returns Cookie domain string or undefined for same-origin
 */
export function getCookieDomain(): string | undefined {
  const config = serverConfig.environment

  // In development with localhost, don't set domain (same-origin only)
  if (config.isDevelopment && config.domain.includes('localhost')) {
    return undefined
  }

  // Special handling for production deployments
  if (config.isProduction) {
    // Check if student and admin domains are the same (single domain deployment)
    if (config.studentDomain === config.adminDomain) {
      // Same domain deployment - don't set cookie domain for security
      return undefined
    }

    // Different domains - extract base domain for subdomain sharing
    const baseDomain = config.domain.split('.').slice(-2).join('.')
    return `.${baseDomain}` // Leading dot allows subdomain sharing
  }

  // For other environments, check if domains are the same
  if (config.studentDomain === config.adminDomain) {
    return undefined
  }

  const baseDomain = config.domain.split('.').slice(-2).join('.')
  return `.${baseDomain}`
}

/**
 * Get secure cookie options for authentication tokens
 * @param maxAge Cookie max age in seconds
 * @returns Cookie options object
 */
export function getSecureCookieOptions(maxAge: number = 3600) {
  const config = serverConfig.environment
  const cookieDomain = getCookieDomain()

  return {
    httpOnly: true,
    secure: config.isProduction, // Only secure in production (HTTPS)
    sameSite: 'lax' as const, // Allows navigation from external sites
    maxAge,
    path: '/',
    ...(cookieDomain && { domain: cookieDomain }), // Only set domain if needed
  }
}

/**
 * Get cookie options for clearing/expiring cookies
 * @returns Cookie options for clearing
 */
export function getClearCookieOptions() {
  const config = serverConfig.environment
  const cookieDomain = getCookieDomain()

  return {
    httpOnly: true,
    secure: config.isProduction,
    sameSite: 'lax' as const,
    maxAge: 0, // Expire immediately
    path: '/',
    ...(cookieDomain && { domain: cookieDomain }),
  }
}

/**
 * Validate cookie security configuration
 * @returns Validation result with warnings
 */
export function validateCookieConfig(): {
  isValid: boolean
  warnings: string[]
  recommendations: string[]
} {
  const config = serverConfig.environment
  const warnings: string[] = []
  const recommendations: string[] = []

  // Check production security
  if (config.isProduction) {
    if (!config.domain || config.domain.includes('localhost')) {
      warnings.push('Production domain not properly configured')
      recommendations.push('Set DOMAIN environment variable to your production domain')
    }

    // Check if HTTPS is likely configured
    if (!process.env.NEXTAUTH_URL?.startsWith('https://')) {
      warnings.push('HTTPS configuration may be missing in production')
      recommendations.push('Ensure HTTPS is configured for secure cookies')
    }
  }

  // Check subdomain configuration
  const studentDomain = config.studentDomain
  const adminDomain = config.adminDomain
  const baseDomain = config.domain

  if (!studentDomain.includes(baseDomain) || !adminDomain.includes(baseDomain)) {
    warnings.push('Subdomain configuration may not support cookie sharing')
    recommendations.push('Ensure subdomains share the same base domain for cookie sharing')
  }

  return {
    isValid: warnings.length === 0,
    warnings,
    recommendations,
  }
}

/**
 * Log cookie configuration for debugging (development only)
 */
export function logCookieConfig(): void {
  if (serverConfig.environment.isProduction) {
    return // Don't log in production
  }

  const cookieDomain = getCookieDomain()
  const validation = validateCookieConfig()

  console.log('🍪 Cookie Configuration:')
  console.log('  Domain:', cookieDomain || 'same-origin')
  console.log('  Secure:', serverConfig.environment.isProduction)
  console.log('  SameSite: lax')
  console.log('  HttpOnly: true')

  if (validation.warnings.length > 0) {
    console.log('⚠️  Warnings:')
    validation.warnings.forEach(warning => console.log(`  - ${warning}`))
  }

  if (validation.recommendations.length > 0) {
    console.log('💡 Recommendations:')
    validation.recommendations.forEach(rec => console.log(`  - ${rec}`))
  }
}

/**
 * Cookie names for different authentication types
 */
export const COOKIE_NAMES = {
  ADMIN_AUTH: 'admin_auth_token',
  ADMIN_REFRESH: 'admin_refresh_token',
  STUDENT_AUTH: 'student_auth_token',
  STUDENT_REFRESH: 'student_refresh_token',
  // Legacy cookies (for cleanup)
  LEGACY_AUTH: 'auth_token',
  LEGACY_REFRESH: 'refresh_token',
} as const

/**
 * Get all cookie names for a specific role
 * @param role User role
 * @returns Array of cookie names for the role
 */
export function getCookieNamesForRole(role: 'admin' | 'student'): string[] {
  if (role === 'admin') {
    return [COOKIE_NAMES.ADMIN_AUTH, COOKIE_NAMES.ADMIN_REFRESH]
  }
  return [COOKIE_NAMES.STUDENT_AUTH, COOKIE_NAMES.STUDENT_REFRESH]
}

/**
 * Get all legacy cookie names for cleanup
 * @returns Array of legacy cookie names
 */
export function getLegacyCookieNames(): string[] {
  return [COOKIE_NAMES.LEGACY_AUTH, COOKIE_NAMES.LEGACY_REFRESH]
}
