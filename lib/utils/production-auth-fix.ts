/**
 * Production Authentication Fixes
 * 
 * Addresses specific issues with login and session management in production
 */

import { NextResponse } from 'next/server'
import { serverConfig } from '@/lib/config'

/**
 * Enhanced cookie options for production deployment
 * Handles single-domain deployments properly
 */
export function getProductionCookieOptions(maxAge: number = 3600) {
  const config = serverConfig.environment
  
  // For production single-domain deployments, use stricter cookie settings
  const cookieOptions = {
    httpOnly: true,
    secure: config.isProduction, // Always secure in production
    sameSite: 'lax' as const,
    maxAge,
    path: '/',
  }
  
  // Only set domain if we have different subdomains
  if (config.studentDomain !== config.adminDomain) {
    const baseDomain = config.domain.split('.').slice(-2).join('.')
    return {
      ...cookieOptions,
      domain: `.${baseDomain}`,
    }
  }
  
  // Same domain deployment - don't set domain for better security
  return cookieOptions
}

/**
 * Enhanced session validation for production
 * Provides better error handling and logging
 */
export function validateProductionSession(sessionData: any): {
  isValid: boolean
  error?: string
  shouldRefresh?: boolean
} {
  if (!sessionData) {
    return {
      isValid: false,
      error: 'Session data not found',
    }
  }
  
  // Check if session is expired
  const now = Date.now()
  const expiresAt = new Date(sessionData.expiresAt).getTime()
  
  if (now > expiresAt) {
    return {
      isValid: false,
      error: 'Session expired',
    }
  }
  
  // Check if session needs refresh (within 10 minutes of expiry)
  const refreshThreshold = 10 * 60 * 1000 // 10 minutes
  const shouldRefresh = (expiresAt - now) < refreshThreshold
  
  return {
    isValid: true,
    shouldRefresh,
  }
}

/**
 * Production-safe login response
 * Ensures proper cookie setting and response format
 */
export function createProductionLoginResponse(
  userData: any,
  sessionData: any,
  tokens: { token: string; refreshToken: string },
  role: 'admin' | 'student'
): NextResponse {
  // Create success response
  const response = NextResponse.json({
    success: true,
    user: {
      id: userData.id,
      username: userData.username,
      name: userData.name,
      role: userData.role,
    },
    session: {
      sessionId: sessionData.sessionId,
      expiresAt: sessionData.expiresAt,
      deviceId: sessionData.deviceId,
    },
  })
  
  // Set cookies with production-safe options
  const cookieOptions = getProductionCookieOptions(60 * 60) // 1 hour
  const refreshCookieOptions = getProductionCookieOptions(7 * 24 * 60 * 60) // 7 days
  
  const authCookieName = role === 'admin' ? 'admin_auth_token' : 'student_auth_token'
  const refreshCookieName = role === 'admin' ? 'admin_refresh_token' : 'student_refresh_token'
  
  response.cookies.set(authCookieName, tokens.token, cookieOptions)
  response.cookies.set(refreshCookieName, tokens.refreshToken, refreshCookieOptions)
  
  // Add security headers
  response.headers.set('X-Content-Type-Options', 'nosniff')
  response.headers.set('X-Frame-Options', 'DENY')
  response.headers.set('X-XSS-Protection', '1; mode=block')
  
  return response
}

/**
 * Production error response with proper cookie clearing
 */
export function createProductionErrorResponse(
  error: string,
  status: number = 401,
  clearCookies: boolean = true
): NextResponse {
  const response = NextResponse.json(
    {
      success: false,
      error,
    },
    { status }
  )
  
  if (clearCookies) {
    // Clear all authentication cookies
    const clearOptions = {
      httpOnly: true,
      secure: serverConfig.environment.isProduction,
      sameSite: 'lax' as const,
      maxAge: 0,
      path: '/',
    }
    
    // Clear both admin and student cookies
    response.cookies.set('admin_auth_token', '', clearOptions)
    response.cookies.set('admin_refresh_token', '', clearOptions)
    response.cookies.set('student_auth_token', '', clearOptions)
    response.cookies.set('student_refresh_token', '', clearOptions)
    
    // Clear legacy cookies
    response.cookies.set('auth_token', '', clearOptions)
    response.cookies.set('refresh_token', '', clearOptions)
  }
  
  return response
}

/**
 * Production session check with enhanced logging
 */
export async function checkProductionSession(
  sessionId: string,
  userId: number,
  sessionRepository: any
): Promise<{
  isValid: boolean
  sessionData?: any
  error?: string
}> {
  try {
    console.log(`[PRODUCTION] Checking session ${sessionId} for user ${userId}`)
    
    const sessionData = await sessionRepository.getSession(sessionId)
    
    if (!sessionData) {
      console.log(`[PRODUCTION] Session ${sessionId} not found in Redis`)
      return {
        isValid: false,
        error: 'Session not found',
      }
    }
    
    // Validate session data
    const validation = validateProductionSession(sessionData)
    
    if (!validation.isValid) {
      console.log(`[PRODUCTION] Session ${sessionId} validation failed: ${validation.error}`)
      return {
        isValid: false,
        error: validation.error,
      }
    }
    
    // Check if user ID matches
    if (sessionData.userId !== userId) {
      console.log(`[PRODUCTION] Session ${sessionId} user mismatch: expected ${userId}, got ${sessionData.userId}`)
      return {
        isValid: false,
        error: 'Session user mismatch',
      }
    }
    
    console.log(`[PRODUCTION] Session ${sessionId} validation successful`)
    
    return {
      isValid: true,
      sessionData,
    }
  } catch (error) {
    console.error(`[PRODUCTION] Session check error for ${sessionId}:`, error)
    return {
      isValid: false,
      error: 'Session check failed',
    }
  }
}

/**
 * Production environment validation
 */
export function validateProductionEnvironment(): {
  isValid: boolean
  warnings: string[]
  errors: string[]
} {
  const warnings: string[] = []
  const errors: string[] = []
  
  const config = serverConfig.environment
  
  // Check required environment variables
  if (!process.env.NODE_ENV || process.env.NODE_ENV !== 'production') {
    errors.push('NODE_ENV must be set to "production"')
  }
  
  if (!config.domain) {
    errors.push('DOMAIN environment variable is required')
  }
  
  if (!config.studentDomain) {
    errors.push('STUDENT_DOMAIN environment variable is required')
  }
  
  if (!config.adminDomain) {
    errors.push('ADMIN_DOMAIN environment variable is required')
  }
  
  if (!process.env.JWT_SECRET) {
    errors.push('JWT_SECRET environment variable is required')
  }
  
  // Check domain consistency
  if (config.studentDomain && config.adminDomain) {
    if (config.studentDomain === config.adminDomain) {
      warnings.push('Student and admin domains are the same - using single domain deployment')
    } else {
      const studentBase = config.studentDomain.split('.').slice(-2).join('.')
      const adminBase = config.adminDomain.split('.').slice(-2).join('.')
      const configBase = config.domain.split('.').slice(-2).join('.')
      
      if (studentBase !== configBase || adminBase !== configBase) {
        warnings.push('Domain configuration may not support proper cookie sharing')
      }
    }
  }
  
  return {
    isValid: errors.length === 0,
    warnings,
    errors,
  }
}

/**
 * Log production environment status
 */
export function logProductionStatus(): void {
  if (!serverConfig.environment.isProduction) {
    return
  }
  
  const validation = validateProductionEnvironment()
  
  console.log('🚀 Production Environment Status:')
  console.log(`   Domain: ${serverConfig.environment.domain}`)
  console.log(`   Student Domain: ${serverConfig.environment.studentDomain}`)
  console.log(`   Admin Domain: ${serverConfig.environment.adminDomain}`)
  console.log(`   Cookie Domain: ${getCookieDomain() || 'same-origin'}`)
  
  if (validation.errors.length > 0) {
    console.log('❌ Production Errors:')
    validation.errors.forEach(error => console.log(`   - ${error}`))
  }
  
  if (validation.warnings.length > 0) {
    console.log('⚠️  Production Warnings:')
    validation.warnings.forEach(warning => console.log(`   - ${warning}`))
  }
  
  if (validation.isValid) {
    console.log('✅ Production environment is properly configured')
  }
}

/**
 * Get cookie domain (re-export for convenience)
 */
function getCookieDomain(): string | undefined {
  const config = serverConfig.environment
  
  if (config.isDevelopment && config.domain.includes('localhost')) {
    return undefined
  }
  
  if (config.isProduction && config.studentDomain === config.adminDomain) {
    return undefined
  }
  
  const baseDomain = config.domain.split('.').slice(-2).join('.')
  return `.${baseDomain}`
}
